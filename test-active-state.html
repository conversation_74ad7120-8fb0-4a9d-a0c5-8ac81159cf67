<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Active State Manager</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .active {
            background-color: #007bff !important;
            color: white !important;
        }
        .btn-active {
            background-color: #28a745 !important;
            color: white !important;
        }
        .test-section {
            margin: 2rem 0;
            padding: 1rem;
            border: 1px solid #ddd;
            border-radius: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h1>Test Active State Manager</h1>
        
        <div class="test-section">
            <h3>Navigation Links</h3>
            <nav>
                <a class="nav-link btn btn-outline-primary m-1" href="./index.html">Homepage</a>
                <a class="nav-link btn btn-outline-primary m-1" href="./main.html">Videos</a>
                <a class="nav-link btn btn-outline-primary m-1" href="./about.html">About</a>
            </nav>
        </div>

        <div class="test-section">
            <h3>Language Buttons</h3>
            <div class="btn-group">
                <button type="button" class="btn btn-outline-secondary" data-language="en">English</button>
                <button type="button" class="btn btn-outline-secondary" data-language="br">Português</button>
                <button type="button" class="btn btn-outline-secondary" data-language="jp">日本語</button>
            </div>
        </div>

        <div class="test-section">
            <h3>Theme Buttons</h3>
            <div class="btn-group">
                <button type="button" class="btn btn-outline-secondary" data-theme-toggle data-theme-value="light">Light</button>
                <button type="button" class="btn btn-outline-secondary" data-theme-toggle data-theme-value="dark">Dark</button>
            </div>
        </div>

        <div class="test-section">
            <h3>Test Controls</h3>
            <button class="btn btn-primary" onclick="testNavigation()">Test Navigation</button>
            <button class="btn btn-success" onclick="testLanguage()">Test Language</button>
            <button class="btn btn-warning" onclick="testTheme()">Test Theme</button>
            <button class="btn btn-info" onclick="showCache()">Show Cache</button>
            <button class="btn btn-danger" onclick="clearCache()">Clear Cache</button>
        </div>

        <div class="test-section">
            <h3>Console Output</h3>
            <div id="console-output" style="background: #f8f9fa; padding: 1rem; border-radius: 0.25rem; font-family: monospace; white-space: pre-wrap; max-height: 300px; overflow-y: auto;"></div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="static/js/includeManager.js"></script>
    <script src="static/js/translations.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="static/js/activeStateManager.js"></script>
    <script src="static/js/languageManager.js"></script>
    <script src="static/js/themeManager.js"></script>
    <script src="static/js/app.js"></script>

    <script>
        // Override console.log to show in page
        const originalLog = console.log;
        const consoleOutput = document.getElementById('console-output');
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            consoleOutput.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        };

        function testNavigation() {
            console.log('Testing Navigation...');
            if (typeof ActiveStateManager !== 'undefined') {
                ActiveStateManager.nav.setActive('main.html');
                console.log('Navigation test completed');
            } else {
                console.log('ActiveStateManager not available');
            }
        }

        function testLanguage() {
            console.log('Testing Language...');
            if (typeof ActiveStateManager !== 'undefined') {
                ActiveStateManager.language.setActive('jp');
                console.log('Language test completed');
            } else {
                console.log('ActiveStateManager not available');
            }
        }

        function testTheme() {
            console.log('Testing Theme...');
            if (typeof ActiveStateManager !== 'undefined') {
                ActiveStateManager.theme.setActive('dark');
                console.log('Theme test completed');
            } else {
                console.log('ActiveStateManager not available');
            }
        }

        function showCache() {
            console.log('Cache contents:');
            console.log('Nav:', localStorage.getItem('activeNavLink'));
            console.log('Language:', localStorage.getItem('activeLanguage'));
            console.log('Theme:', localStorage.getItem('activeTheme'));
        }

        function clearCache() {
            localStorage.removeItem('activeNavLink');
            localStorage.removeItem('activeLanguage');
            localStorage.removeItem('activeTheme');
            console.log('Cache cleared');
        }

        // Test on load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Page loaded, testing Active State Manager...');
            setTimeout(() => {
                if (typeof ActiveStateManager !== 'undefined') {
                    console.log('ActiveStateManager is available');
                    console.log('Available methods:', Object.keys(ActiveStateManager));
                } else {
                    console.log('ActiveStateManager is NOT available');
                }
            }, 1000);
        });
    </script>
</body>
</html>
