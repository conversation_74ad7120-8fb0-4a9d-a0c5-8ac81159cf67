/* ===================================
   Utility Classes - Bootstrap Compatible
   =================================== */

/* ===== SPACING UTILITIES ===== */
/* Margin utilities using design tokens */
.m-0 { margin: 0 !important; }
.m-1 { margin: var(--space-xs) !important; }
.m-2 { margin: var(--space-sm) !important; }
.m-3 { margin: var(--space-md) !important; }
.m-4 { margin: var(--space-lg) !important; }
.m-5 { margin: var(--space-xl) !important; }

/* Margin top */
.mt-0 { margin-top: 0 !important; }
.mt-1 { margin-top: var(--space-xs) !important; }
.mt-2 { margin-top: var(--space-sm) !important; }
.mt-3 { margin-top: var(--space-md) !important; }
.mt-4 { margin-top: var(--space-lg) !important; }
.mt-5 { margin-top: var(--space-xl) !important; }

/* Margin bottom */
.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: var(--space-xs) !important; }
.mb-2 { margin-bottom: var(--space-sm) !important; }
.mb-3 { margin-bottom: var(--space-md) !important; }
.mb-4 { margin-bottom: var(--space-lg) !important; }
.mb-5 { margin-bottom: var(--space-xl) !important; }

/* Padding utilities */
.p-0 { padding: 0 !important; }
.p-1 { padding: var(--space-xs) !important; }
.p-2 { padding: var(--space-sm) !important; }
.p-3 { padding: var(--space-md) !important; }
.p-4 { padding: var(--space-lg) !important; }
.p-5 { padding: var(--space-xl) !important; }

/* Padding top */
.pt-0 { padding-top: 0 !important; }
.pt-1 { padding-top: var(--space-xs) !important; }
.pt-2 { padding-top: var(--space-sm) !important; }
.pt-3 { padding-top: var(--space-md) !important; }
.pt-4 { padding-top: var(--space-lg) !important; }
.pt-5 { padding-top: var(--space-xl) !important; }

/* Padding bottom */
.pb-0 { padding-bottom: 0 !important; }
.pb-1 { padding-bottom: var(--space-xs) !important; }
.pb-2 { padding-bottom: var(--space-sm) !important; }
.pb-3 { padding-bottom: var(--space-md) !important; }
.pb-4 { padding-bottom: var(--space-lg) !important; }
.pb-5 { padding-bottom: var(--space-xl) !important; }

/* ===== DISPLAY UTILITIES ===== */
.d-none { display: none !important; }
.d-inline { display: inline !important; }
.d-inline-block { display: inline-block !important; }
.d-block { display: block !important; }
.d-flex { display: flex !important; }
.d-inline-flex { display: inline-flex !important; }
.d-grid { display: grid !important; }

/* ===== FLEXBOX UTILITIES ===== */
.flex-row { flex-direction: row !important; }
.flex-column { flex-direction: column !important; }
.flex-wrap { flex-wrap: wrap !important; }
.flex-nowrap { flex-wrap: nowrap !important; }

.justify-content-start { justify-content: flex-start !important; }
.justify-content-end { justify-content: flex-end !important; }
.justify-content-center { justify-content: center !important; }
.justify-content-between { justify-content: space-between !important; }
.justify-content-around { justify-content: space-around !important; }
.justify-content-evenly { justify-content: space-evenly !important; }

.align-items-start { align-items: flex-start !important; }
.align-items-end { align-items: flex-end !important; }
.align-items-center { align-items: center !important; }
.align-items-baseline { align-items: baseline !important; }
.align-items-stretch { align-items: stretch !important; }

/* ===== TEXT UTILITIES ===== */
.text-start { text-align: start !important; }
.text-end { text-align: end !important; }
.text-center { text-align: center !important; }
.text-justify { text-align: justify !important; }

.text-lowercase { text-transform: lowercase !important; }
.text-uppercase { text-transform: uppercase !important; }
.text-capitalize { text-transform: capitalize !important; }

.fw-light { font-weight: var(--font-weight-light) !important; }
.fw-normal { font-weight: var(--font-weight-normal) !important; }
.fw-medium { font-weight: var(--font-weight-medium) !important; }
.fw-semibold { font-weight: var(--font-weight-semibold) !important; }
.fw-bold { font-weight: var(--font-weight-bold) !important; }

.fs-1 { font-size: var(--font-size-5xl) !important; }
.fs-2 { font-size: var(--font-size-4xl) !important; }
.fs-3 { font-size: var(--font-size-3xl) !important; }
.fs-4 { font-size: var(--font-size-2xl) !important; }
.fs-5 { font-size: var(--font-size-xl) !important; }
.fs-6 { font-size: var(--font-size-lg) !important; }

/* ===== COLOR UTILITIES ===== */
.text-primary { color: var(--accent-primary) !important; }
.text-secondary { color: var(--color-gray-600) !important; }
.text-success { color: var(--color-success) !important; }
.text-warning { color: var(--color-warning) !important; }
.text-danger { color: var(--color-error) !important; }
.text-info { color: var(--color-info) !important; }
.text-light { color: var(--color-gray-300) !important; }
.text-dark { color: var(--color-gray-800) !important; }
.text-muted { color: var(--muted-text-color) !important; }
.text-white { color: var(--color-white) !important; }
.text-black { color: var(--color-black) !important; }

/* Background colors */
.bg-primary { background-color: var(--accent-primary) !important; }
.bg-secondary { background-color: var(--color-gray-600) !important; }
.bg-success { background-color: var(--color-success) !important; }
.bg-warning { background-color: var(--color-warning) !important; }
.bg-danger { background-color: var(--color-error) !important; }
.bg-info { background-color: var(--color-info) !important; }
.bg-light { background-color: var(--color-gray-100) !important; }
.bg-dark { background-color: var(--color-gray-800) !important; }
.bg-white { background-color: var(--color-white) !important; }
.bg-transparent { background-color: transparent !important; }

/* ===== BORDER UTILITIES ===== */
.border { border: 1px solid var(--border-color) !important; }
.border-0 { border: 0 !important; }
.border-top { border-top: 1px solid var(--border-color) !important; }
.border-end { border-right: 1px solid var(--border-color) !important; }
.border-bottom { border-bottom: 1px solid var(--border-color) !important; }
.border-start { border-left: 1px solid var(--border-color) !important; }

.rounded { border-radius: var(--radius-md) !important; }
.rounded-0 { border-radius: 0 !important; }
.rounded-1 { border-radius: var(--radius-sm) !important; }
.rounded-2 { border-radius: var(--radius-md) !important; }
.rounded-3 { border-radius: var(--radius-lg) !important; }
.rounded-4 { border-radius: var(--radius-xl) !important; }
.rounded-5 { border-radius: var(--radius-2xl) !important; }
.rounded-circle { border-radius: var(--radius-full) !important; }
.rounded-pill { border-radius: 50rem !important; }

/* ===== SHADOW UTILITIES ===== */
.shadow-none { box-shadow: none !important; }
.shadow-sm { box-shadow: var(--shadow-sm) !important; }
.shadow { box-shadow: var(--shadow-md) !important; }
.shadow-lg { box-shadow: var(--shadow-lg) !important; }
.shadow-xl { box-shadow: var(--shadow-xl) !important; }

/* ===== POSITION UTILITIES ===== */
.position-static { position: static !important; }
.position-relative { position: relative !important; }
.position-absolute { position: absolute !important; }
.position-fixed { position: fixed !important; }
.position-sticky { position: sticky !important; }

/* ===== OVERFLOW UTILITIES ===== */
.overflow-auto { overflow: auto !important; }
.overflow-hidden { overflow: hidden !important; }
.overflow-visible { overflow: visible !important; }
.overflow-scroll { overflow: scroll !important; }

/* ===== ANIMATION UTILITIES ===== */
.fade-in { animation: var(--animation-fade-in); }
.slide-up { animation: var(--animation-slide-up); }
.scale-in { animation: var(--animation-scale-in); }

/* Transition utilities */
.transition-all { transition: all var(--transition-normal) var(--ease-out) !important; }
.transition-colors { transition: color var(--transition-normal) var(--ease-out), 
                                background-color var(--transition-normal) var(--ease-out),
                                border-color var(--transition-normal) var(--ease-out) !important; }
.transition-opacity { transition: opacity var(--transition-normal) var(--ease-out) !important; }
.transition-transform { transition: transform var(--transition-normal) var(--ease-out) !important; }

/* ===== RESPONSIVE UTILITIES ===== */
/* Hide/show on different screen sizes */
@media (max-width: 575.98px) {
  .d-sm-none { display: none !important; }
}

@media (min-width: 576px) {
  .d-sm-block { display: block !important; }
  .d-sm-flex { display: flex !important; }
}

@media (max-width: 767.98px) {
  .d-md-none { display: none !important; }
}

@media (min-width: 768px) {
  .d-md-block { display: block !important; }
  .d-md-flex { display: flex !important; }
}

@media (max-width: 991.98px) {
  .d-lg-none { display: none !important; }
}

@media (min-width: 992px) {
  .d-lg-block { display: block !important; }
  .d-lg-flex { display: flex !important; }
}

/* ===== ACCESSIBILITY UTILITIES ===== */
.visually-hidden {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

.visually-hidden-focusable:focus,
.visually-hidden-focusable:focus-within {
  position: static !important;
  width: auto !important;
  height: auto !important;
  overflow: visible !important;
  clip: auto !important;
  white-space: normal !important;
}

/* Focus utilities */
.focus-ring:focus {
  outline: 0;
  box-shadow: var(--focus-ring);
}

/* ===== INTERACTION UTILITIES ===== */
.user-select-all { user-select: all !important; }
.user-select-auto { user-select: auto !important; }
.user-select-none { user-select: none !important; }

.pointer-events-none { pointer-events: none !important; }
.pointer-events-auto { pointer-events: auto !important; }

.cursor-pointer { cursor: pointer !important; }
.cursor-default { cursor: default !important; }
.cursor-not-allowed { cursor: not-allowed !important; }
