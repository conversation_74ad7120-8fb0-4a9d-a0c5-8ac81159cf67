/* ===================================
   Container Layout Styles
   =================================== */

/* Navigation buttons */
#up, #down {
  margin: 0;
  color: var(--accent-primary);
  padding: 2rem;
  background-color: var(--card-bg-color);
  border: 1px solid var(--border-color);
  transition: all var(--transition-normal) var(--ease-out);

  &:hover {
    background-color: var(--hover-bg-color);
    color: var(--accent-primary-hover);
    border-color: var(--accent-primary);
  }
}

/* Video sections */
#youtube, .video-links {
  text-align: center;
}

.video-links {
  padding: 1rem;
  font-size: 1rem;
  font-weight: bold;

  a {
    padding: 0.5rem;
    border-radius: 1rem;
    color: var(--accent-primary);
    transition: all var(--transition-normal) var(--ease-out);
    border: 2px solid transparent;

    &:hover {
      border: 2px solid var(--accent-primary);
      color: var(--accent-primary-hover);
      background-color: var(--hover-bg-color);
    }
  }
}

/* Video iframe */
iframe {
  aspect-ratio: 16/9;
  animation: fadeIn 5s var(--ease-in-out);
}

/* Link animations */
.link {
  animation: fadeIn 7s var(--ease-in-out);
}

/* Active states */
.active {
  color: var(--accent-primary);
}

/* Active state for buttons */
.btn-active {
  background-color: var(--accent-primary) !important;
  color: var(--text-on-accent) !important;
  border-color: var(--accent-primary) !important;
}

/* Language and theme button active states */
[data-language].active,
[data-language].btn-active {
  background-color: var(--accent-primary) !important;
  color: var(--text-on-accent) !important;
  border-color: var(--accent-primary) !important;
  transform: scale(1.05);
  transition: all 0.2s ease;
}

[data-theme-toggle].active,
[data-theme-toggle].btn-active {
  background-color: var(--accent-primary) !important;
  color: var(--text-on-accent) !important;
  border-color: var(--accent-primary) !important;
  transform: scale(1.05);
  transition: all 0.2s ease;
}

/* Navigation link active state enhancement */
.nav-link.active {
  color: var(--accent-primary) !important;
  font-weight: 600;
  position: relative;
}

.nav-link.active::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 2px;
  background-color: var(--accent-primary);
  border-radius: 1px;
}