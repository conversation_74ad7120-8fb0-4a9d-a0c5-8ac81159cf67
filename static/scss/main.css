@charset "UTF-8";
/* ===================================
   Main SCSS Entry Point - Enhanced Architecture
   =================================== */
/* ===================================
   Enhanced CSS Custom Properties with OKLCH Colors
   =================================== */
/* OKLCH Color System
   OKLCH provides better perceptual uniformity and wider color gamut
   Format: oklch(lightness chroma hue / alpha)
   - Lightness: 0-1 (0 = black, 1 = white)
   - Chroma: 0+ (0 = grayscale, higher = more saturated)
   - Hue: 0-360 degrees
*/
:root {
  /* ===== DESIGN TOKENS - SPACING ===== */
  --space-xs: 0.25rem; /* 4px */
  --space-sm: 0.5rem; /* 8px */
  --space-md: 1rem; /* 16px */
  --space-lg: 1.5rem; /* 24px */
  --space-xl: 2rem; /* 32px */
  --space-2xl: 3rem; /* 48px */
  --space-3xl: 4rem; /* 64px */
  --space-4xl: 6rem; /* 96px */
  /* ===== DESIGN TOKENS - BORDER RADIUS ===== */
  --radius-none: 0;
  --radius-sm: 0.125rem; /* 2px */
  --radius-md: 0.25rem; /* 4px */
  --radius-lg: 0.5rem; /* 8px */
  --radius-xl: 0.75rem; /* 12px */
  --radius-2xl: 1rem; /* 16px */
  --radius-full: 9999px;
  /* ===== DESIGN TOKENS - SHADOWS ===== */
  --shadow-sm: 0 1px 2px 0 oklch(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px oklch(0 0 0 / 0.1), 0 2px 4px -2px oklch(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px oklch(0 0 0 / 0.1), 0 4px 6px -4px oklch(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px oklch(0 0 0 / 0.1), 0 8px 10px -6px oklch(0 0 0 / 0.1);
  /* ===== PRIMARY COLORS ===== */
  /* Red accent colors using OKLCH */
  --accent-color: oklch(0.55 0.22 25); /* Bright red #ff0800 equivalent */
  --accent-hover-color: oklch(0.45 0.20 25); /* Darker red #cc0600 equivalent */
  --accent-dark-color: oklch(0.60 0.18 25); /* Dark theme red #ff4444 equivalent */
  --accent-dark-hover-color: oklch(0.70 0.16 25); /* Dark theme hover #ff6666 equivalent */
  /* ===== NEUTRAL COLORS ===== */
  /* Pure whites and blacks */
  --color-white: oklch(1.0 0 0); /* Pure white */
  --color-black: oklch(0.0 0 0); /* Pure black */
  /* Grays with slight warm tint for better visual harmony */
  --color-gray-50: oklch(0.98 0.005 85);
  --color-gray-100: oklch(0.95 0.005 85);
  --color-gray-200: oklch(0.90 0.005 85);
  --color-gray-300: oklch(0.80 0.005 85);
  --color-gray-400: oklch(0.65 0.005 85);
  --color-gray-500: oklch(0.50 0.005 85);
  --color-gray-600: oklch(0.40 0.005 85);
  --color-gray-700: oklch(0.30 0.005 85);
  --color-gray-800: oklch(0.20 0.005 85);
  --color-gray-900: oklch(0.10 0.005 85);
  /* ===== SEMANTIC COLORS ===== */
  /* Success colors */
  --color-success: oklch(0.65 0.15 145); /* Green */
  --color-success-hover: oklch(0.55 0.15 145);
  /* Warning/favorite colors */
  --color-warning: oklch(0.80 0.15 85); /* Golden yellow #ffc107 equivalent */
  --color-warning-hover: oklch(0.85 0.15 85); /* Lighter golden #ffcd39 equivalent */
  /* Error colors */
  --color-error: oklch(0.55 0.22 25); /* Red - matches accent */
  --color-error-hover: oklch(0.45 0.20 25);
  /* Info colors */
  --color-info: oklch(0.60 0.15 250); /* Blue */
  --color-info-hover: oklch(0.50 0.15 250);
  /* ===== ALPHA/TRANSPARENCY VALUES ===== */
  --alpha-light: 0.05;
  --alpha-medium: 0.125;
  --alpha-heavy: 0.25;
  --alpha-backdrop: 0.95;
  /* ===== ANIMATION AND TRANSITION VARIABLES ===== */
  /* Transition durations */
  --transition-fast: 0.15s;
  --transition-normal: 0.25s;
  --transition-slow: 0.35s;
  --transition-slower: 0.5s;
  /* Easing functions - optimized for better UX */
  --ease-out: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --ease-in-out: cubic-bezier(0.645, 0.045, 0.355, 1);
  --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --ease-smooth: cubic-bezier(0.4, 0, 0.2, 1);
  /* Animation delays for staggered effects */
  --stagger-delay-1: 0.05s;
  --stagger-delay-2: 0.1s;
  --stagger-delay-3: 0.15s;
  --stagger-delay-4: 0.2s;
  --stagger-delay-5: 0.25s;
  --stagger-delay-6: 0.3s;
  /* ===== TYPOGRAPHY SCALE ===== */
  --font-size-xs: 0.75rem; /* 12px */
  --font-size-sm: 0.875rem; /* 14px */
  --font-size-base: 1rem; /* 16px */
  --font-size-lg: 1.125rem; /* 18px */
  --font-size-xl: 1.25rem; /* 20px */
  --font-size-2xl: 1.5rem; /* 24px */
  --font-size-3xl: 1.875rem; /* 30px */
  --font-size-4xl: 2.25rem; /* 36px */
  --font-size-5xl: 3rem; /* 48px */
  --font-size-6xl: 3.75rem; /* 60px */
  /* Line heights */
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;
  /* Font weights */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  /* ===== Z-INDEX SCALE ===== */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
  /* Bootstrap compatibility */
  --bs-white: var(--color-white);
  --bs-black: var(--color-black);
}

/* ===== LIGHT THEME VARIABLES ===== */
:root,
[data-theme=light] {
  /* Background colors */
  --bg-color: var(--color-white);
  --card-bg-color: var(--color-white);
  --navbar-bg-color: oklch(from var(--color-white) l c h / var(--alpha-backdrop));
  /* Text colors */
  --text-color: var(--color-black);
  --muted-text-color: var(--color-gray-500);
  /* Interactive colors */
  --accent-primary: var(--accent-color);
  --accent-primary-hover: var(--accent-hover-color);
  /* Border and shadow colors */
  --border-color: oklch(from var(--color-black) l c h / var(--alpha-medium));
  --hover-bg-color: oklch(from var(--color-black) l c h / var(--alpha-light));
  --shadow-color-d: oklch(from var(--color-black) l c h / 0.051);
  --shadow-color-l: oklch(from var(--color-white) l c h / var(--alpha-light));
  /* Focus ring colors */
  --focus-ring: oklch(from var(--accent-color) l c h / var(--alpha-heavy));
}

/* ===== DARK THEME VARIABLES ===== */
[data-theme=dark],
body.dark {
  /* Background colors */
  --bg-color: var(--color-black);
  --card-bg-color: var(--color-gray-900);
  --navbar-bg-color: oklch(from var(--color-black) l c h / var(--alpha-backdrop));
  /* Text colors */
  --text-color: var(--color-white);
  --muted-text-color: var(--color-gray-400);
  /* Interactive colors */
  --accent-primary: var(--accent-dark-color);
  --accent-primary-hover: var(--accent-dark-hover-color);
  /* Border and shadow colors */
  --border-color: oklch(from var(--color-white) l c h / var(--alpha-medium));
  --hover-bg-color: oklch(from var(--color-white) l c h / var(--alpha-light));
  --shadow-color-d: oklch(from var(--color-white) l c h / 0.051);
  --shadow-color-l: oklch(from var(--color-black) l c h / var(--alpha-light));
  /* Focus ring colors */
  --focus-ring: oklch(from var(--accent-dark-color) l c h / var(--alpha-heavy));
}

/* ===================================
   Design Tokens - Consistent Design System
   =================================== */
/* ===== BREAKPOINTS ===== */
/* Bootstrap 5.3 compatible breakpoints */
/* CSS Custom Properties for breakpoints (for use in calc() functions) */
:root {
  --breakpoint-xs: 0px;
  --breakpoint-sm: 576px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 992px;
  --breakpoint-xl: 1200px;
  --breakpoint-xxl: 1400px;
}

/* ===== CONTAINER SIZES ===== */
/* ===== COMPONENT SPECIFIC TOKENS ===== */
:root {
  /* Navigation */
  --navbar-height: 4rem;
  --navbar-padding-y: var(--space-md);
  --navbar-padding-x: var(--space-lg);
  --navbar-brand-height: 2.5rem;
  /* Cards */
  --card-padding: var(--space-lg);
  --card-border-radius: var(--radius-lg);
  --card-border-width: 1px;
  --card-shadow: var(--shadow-md);
  --card-hover-shadow: var(--shadow-lg);
  /* Buttons */
  --btn-padding-y: var(--space-sm);
  --btn-padding-x: var(--space-md);
  --btn-font-size: var(--font-size-base);
  --btn-line-height: var(--line-height-normal);
  --btn-border-radius: var(--radius-md);
  --btn-border-width: 1px;
  /* Forms */
  --input-padding-y: var(--space-sm);
  --input-padding-x: var(--space-md);
  --input-font-size: var(--font-size-base);
  --input-line-height: var(--line-height-normal);
  --input-border-radius: var(--radius-md);
  --input-border-width: 1px;
  --input-focus-border-width: 2px;
  /* Modals */
  --modal-backdrop-opacity: 0.5;
  --modal-border-radius: var(--radius-lg);
  --modal-padding: var(--space-xl);
  /* Tooltips */
  --tooltip-max-width: 200px;
  --tooltip-padding-y: var(--space-xs);
  --tooltip-padding-x: var(--space-sm);
  --tooltip-font-size: var(--font-size-sm);
  --tooltip-border-radius: var(--radius-sm);
  /* Alerts */
  --alert-padding-y: var(--space-md);
  --alert-padding-x: var(--space-lg);
  --alert-border-radius: var(--radius-md);
  --alert-border-width: 1px;
}

/* ===== UTILITY CLASSES TOKENS ===== */
:root {
  /* Spacing utilities - matches Bootstrap but with our tokens */
  --spacer: var(--space-md); /* 1rem base */
  /* Display utilities */
  --display-block: block;
  --display-inline: inline;
  --display-inline-block: inline-block;
  --display-flex: flex;
  --display-inline-flex: inline-flex;
  --display-grid: grid;
  --display-none: none;
  /* Flex utilities */
  --flex-direction-row: row;
  --flex-direction-column: column;
  --flex-wrap: wrap;
  --flex-nowrap: nowrap;
  --justify-content-start: flex-start;
  --justify-content-end: flex-end;
  --justify-content-center: center;
  --justify-content-between: space-between;
  --justify-content-around: space-around;
  --align-items-start: flex-start;
  --align-items-end: flex-end;
  --align-items-center: center;
  --align-items-stretch: stretch;
}

/* ===== ANIMATION PRESETS ===== */
:root {
  /* Fade animations */
  --animation-fade-in: fadeIn var(--transition-normal) var(--ease-out);
  --animation-fade-out: fadeOut var(--transition-normal) var(--ease-out);
  /* Slide animations */
  --animation-slide-up: slideInUp var(--transition-normal) var(--ease-out);
  --animation-slide-down: slideInDown var(--transition-normal) var(--ease-out);
  --animation-slide-left: slideInLeft var(--transition-normal) var(--ease-out);
  --animation-slide-right: slideInRight var(--transition-normal) var(--ease-out);
  /* Scale animations */
  --animation-scale-in: scaleIn var(--transition-normal) var(--ease-bounce);
  --animation-scale-out: scaleOut var(--transition-normal) var(--ease-out);
  /* Rotation animations */
  --animation-spin: spin 1s linear infinite;
  --animation-pulse: pulse 2s var(--ease-in-out) infinite;
}

/* ===== FOCUS MANAGEMENT ===== */
:root {
  /* Focus ring styles for accessibility */
  --focus-ring-width: 2px;
  --focus-ring-offset: 2px;
  --focus-ring-color: var(--accent-primary);
  --focus-ring-style: solid;
  --focus-ring: var(--focus-ring-width) var(--focus-ring-style) var(--focus-ring-color);
  /* Focus ring for different themes */
  --focus-ring-light: 2px solid oklch(from var(--accent-color) l c h / 0.5);
  --focus-ring-dark: 2px solid oklch(from var(--accent-dark-color) l c h / 0.5);
}

/* ===== PRINT STYLES TOKENS ===== */
@media print {
  :root {
    --print-font-size: 12pt;
    --print-line-height: 1.4;
    --print-margin: 0.5in;
  }
}
/* ===================================
   Base Styles and Resets
   =================================== */
/* Apply theme colors to html and body */
html {
  background-color: var(--bg-color);
  color: var(--text-color);
  transition: background-color var(--transition-normal) var(--ease-out), color var(--transition-normal) var(--ease-out);
}

body {
  background-color: var(--bg-color);
  color: var(--text-color);
  transition: background-color var(--transition-normal) var(--ease-out), color var(--transition-normal) var(--ease-out);
}

/* Global link styles */
a {
  border-radius: 0.5rem;
  text-decoration: none;
  transition: all var(--transition-normal) var(--ease-out);
}

/* SVG color inheritance */
svg {
  color: var(--text-color);
  transition: color var(--transition-normal) var(--ease-out);
}

/* Global transition for interactive elements */
button,
.page-link {
  transition: all var(--transition-normal) var(--ease-out);
}

/* Container styles */
.container {
  background-color: var(--bg-color);
  color: var(--text-color);
}

/* ===================================
   Typography Styles
   =================================== */
/* Headings and text alignment */
h1, h2, h3, h4, h5, p {
  text-align: center;
}

/* Main heading styles */
h1 {
  color: var(--accent-primary);
  font-size: 3rem;
  animation: fadeIn 3s var(--ease-in-out);
}

/* Secondary headings */
h2, h3, h4 {
  color: var(--text-color);
  font-size: 2rem;
  animation: fadeIn 3s var(--ease-in-out);
}

/* All headings animation */
h1, h2, h3, h4, h5 {
  animation: fadeIn 3s var(--ease-in-out);
}

/* Lead paragraph styles */
p.lead {
  font-size: 1.2rem;
  margin-bottom: 1rem;
  animation: fadeIn 5s var(--ease-in-out);
}

/* ===================================
   Container Layout Styles
   =================================== */
/* Navigation buttons */
#up, #down {
  margin: 0;
  color: var(--accent-primary);
  padding: 2rem;
  background-color: var(--card-bg-color);
  border: 1px solid var(--border-color);
  transition: all var(--transition-normal) var(--ease-out);
}
#up:hover, #down:hover {
  background-color: var(--hover-bg-color);
  color: var(--accent-primary-hover);
  border-color: var(--accent-primary);
}

/* Video sections */
#youtube, .video-links {
  text-align: center;
}

.video-links {
  padding: 1rem;
  font-size: 1rem;
  font-weight: bold;
}
.video-links a {
  padding: 0.5rem;
  border-radius: 1rem;
  color: var(--accent-primary);
  transition: all var(--transition-normal) var(--ease-out);
  border: 2px solid transparent;
}
.video-links a:hover {
  border: 2px solid var(--accent-primary);
  color: var(--accent-primary-hover);
  background-color: var(--hover-bg-color);
}

/* Video iframe */
iframe {
  aspect-ratio: 16/9;
  animation: fadeIn 5s var(--ease-in-out);
}

/* Link animations */
.link {
  animation: fadeIn 7s var(--ease-in-out);
}

/* Active states */
.active {
  color: var(--accent-primary);
}

/* Active state for buttons */
.btn-active {
  background-color: var(--accent-primary) !important;
  color: var(--text-on-accent) !important;
  border-color: var(--accent-primary) !important;
}

/* Language and theme button active states */
[data-language].active,
[data-language].btn-active {
  background-color: var(--accent-primary) !important;
  color: var(--text-on-accent) !important;
  border-color: var(--accent-primary) !important;
  transform: scale(1.05);
  transition: all 0.2s ease;
}

[data-theme-toggle].active,
[data-theme-toggle].btn-active {
  background-color: var(--accent-primary) !important;
  color: var(--text-on-accent) !important;
  border-color: var(--accent-primary) !important;
  transform: scale(1.05);
  transition: all 0.2s ease;
}

/* Navigation link active state enhancement */
.nav-link.active {
  color: var(--accent-primary) !important;
  font-weight: 600;
  position: relative;
}

.nav-link.active::after {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 2px;
  background-color: var(--accent-primary);
  border-radius: 1px;
}

/* ===================================
   Navbar Layout Styles
   =================================== */
.navbar {
  background-color: var(--navbar-bg-color);
  padding: 0.5rem;
  border-bottom: 1px solid var(--border-color);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
}

.nav-link {
  background-color: transparent;
  color: var(--text-color);
  border: none;
  transition: all var(--transition-normal) var(--ease-out);
  border-radius: 0.375rem;
  padding: 0.5rem 1rem;
}
.nav-link:hover {
  color: var(--accent-primary);
  background-color: var(--hover-bg-color);
  text-shadow: 0 0 3px var(--accent-primary);
}
.nav-link:focus {
  color: var(--accent-primary);
  background-color: var(--hover-bg-color);
}

.navbar-nav {
  flex-direction: row;
}

.nav-item {
  padding-right: 1rem;
}

.navbar-brand {
  padding: 0.5rem;
}
.navbar-brand img {
  width: 100px;
}

/* Language selector and switcher */
#langselect, #swicher {
  padding: 0.5rem;
}

/* ===================================
   Button Component Styles
   =================================== */
/* Base button styles */
button {
  border: none;
  padding: 1rem;
  transition: all var(--transition-normal) var(--ease-out);
}

/* Enhanced Bootstrap Button Styles with Theme Support */
.btn-primary {
  --bs-btn-font-weight: 600;
  --bs-btn-color: var(--bs-white);
  --bs-btn-bg: var(--accent-primary);
  --bs-btn-border-color: var(--accent-primary);
  --bs-btn-hover-color: var(--bs-white);
  --bs-btn-hover-bg: var(--accent-primary-hover);
  --bs-btn-hover-border-color: var(--accent-primary-hover);
  --bs-btn-active-color: var(--bs-white);
  --bs-btn-active-bg: var(--accent-primary-hover);
  --bs-btn-active-border-color: var(--accent-primary-hover);
}

/* Theme-aware outline buttons */
.btn-outline-primary {
  --bs-btn-color: var(--accent-primary);
  --bs-btn-border-color: var(--accent-primary);
  --bs-btn-hover-color: var(--bs-white);
  --bs-btn-hover-bg: var(--accent-primary);
  --bs-btn-hover-border-color: var(--accent-primary);
  --bs-btn-active-color: var(--bs-white);
  --bs-btn-active-bg: var(--accent-primary);
  --bs-btn-active-border-color: var(--accent-primary);
}

.btn-outline-secondary {
  --bs-btn-color: var(--text-color);
  --bs-btn-border-color: var(--border-color);
  --bs-btn-hover-color: var(--bg-color);
  --bs-btn-hover-bg: var(--text-color);
  --bs-btn-hover-border-color: var(--text-color);
  --bs-btn-active-color: var(--bg-color);
  --bs-btn-active-bg: var(--text-color);
  --bs-btn-active-border-color: var(--text-color);
}

/* Theme Toggle Button Styles */
.theme-toggle-btn {
  border: 1px solid var(--border-color);
  background-color: var(--card-bg-color);
  color: var(--text-color);
  transition: all var(--transition-normal) var(--ease-out);
}
.theme-toggle-btn:hover {
  background-color: var(--hover-bg-color);
  border-color: var(--accent-primary);
  color: var(--accent-primary);
}
.theme-toggle-btn.active {
  background-color: var(--accent-primary);
  border-color: var(--accent-primary);
  color: var(--bs-white);
}

/* Favorite Button Styles */
.favorite-btn {
  transition: all var(--transition-fast) var(--ease-out);
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.favorite-btn:hover {
  transform: scale(1.1);
}
.favorite-btn.active {
  background-color: var(--color-warning);
  border-color: var(--color-warning);
  color: var(--color-black);
}
.favorite-btn.active:hover {
  background-color: var(--color-warning-hover);
  border-color: var(--color-warning-hover);
}

/* Load video button */
.load-video-btn {
  transition: all var(--transition-fast) var(--ease-out);
}
.load-video-btn:hover {
  transform: scale(1.05);
}

/* View Toggle Button */
#viewToggle {
  transition: all var(--transition-fast) var(--ease-out);
  border-radius: 0.375rem;
}
#viewToggle:hover {
  transform: scale(1.05);
}

/* Refresh Button Animation */
#refreshButton .fa-sync-alt {
  transition: transform var(--transition-normal) var(--ease-out);
}
#refreshButton:hover .fa-sync-alt {
  transform: rotate(180deg);
}

/* Responsive button adjustments */
@media (max-width: 576px) {
  .favorite-btn {
    width: 32px;
    height: 32px;
  }
}
/* ===================================
   Form Component Styles
   =================================== */
/* Input group text styling */
.input-group-text {
  transition: all var(--transition-fast) var(--ease-out);
  border: 1px solid var(--border-color);
  background-color: var(--card-bg-color);
  color: var(--text-color);
}

/* Input placeholder styles */
.input-group .form-control::-moz-placeholder {
  color: var(--muted-text-color);
}
.input-group .form-control::placeholder {
  color: var(--muted-text-color);
}

/* Search Input Enhancements */
#youtubeSearch {
  transition: all var(--transition-fast) var(--ease-out);
  background-color: var(--card-bg-color);
  border-color: var(--border-color);
  color: var(--text-color);
}
#youtubeSearch:focus {
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 0.2rem var(--focus-ring);
  background-color: var(--card-bg-color);
  color: var(--text-color);
}

/* ===================================
   Card Component Styles
   =================================== */
/* Base card styles */
.card, .card.shadow-sm, .card.shadow-lg {
  transition: transform var(--transition-fast) var(--ease-out), box-shadow var(--transition-fast) var(--ease-out), color var(--transition-normal) var(--ease-out);
  border: 1px solid var(--border-color);
  background-color: var(--card-bg-color);
  color: var(--text-color);
}
.card .video-placeholder, .card.shadow-sm .video-placeholder, .card.shadow-lg .video-placeholder {
  background-color: var(--bg-color);
}
.card .card-title, .card.shadow-sm .card-title, .card.shadow-lg .card-title {
  color: var(--text-color);
  transition: color var(--transition-normal) var(--ease-out);
}

/* Video Container Enhancements */
#videoContainer {
  min-height: 200px;
  transition: all var(--transition-normal) var(--ease-out);
}
#videoContainer .card {
  animation: slideInUp 0.3s var(--ease-out) forwards;
}
#videoContainer .card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px var(--shadow-color-d);
}
#videoContainer .card:nth-child(1) {
  animation-delay: var(--stagger-delay-1);
}
#videoContainer .card:nth-child(2) {
  animation-delay: var(--stagger-delay-2);
}
#videoContainer .card:nth-child(3) {
  animation-delay: var(--stagger-delay-3);
}
#videoContainer .card:nth-child(4) {
  animation-delay: var(--stagger-delay-4);
}
#videoContainer .card:nth-child(5) {
  animation-delay: var(--stagger-delay-5);
}
#videoContainer .card:nth-child(6) {
  animation-delay: var(--stagger-delay-6);
}

/* Dark theme card hover adjustments */
[data-theme=dark] #videoContainer .card:hover {
  box-shadow: 0 4px 12px oklch(from var(--color-white) l c h/0.1);
}

/* Favorite Card Border */
.card.border-warning {
  border-width: 2px !important;
  position: relative;
}
.card.border-warning::before {
  content: "⭐";
  position: absolute;
  top: -8px;
  right: -8px;
  background: var(--color-warning);
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  z-index: 1;
}

/* Responsive card adjustments */
@media (max-width: 576px) {
  #videoContainer .card {
    margin-bottom: 1rem;
  }
}
/* ===================================
   Theme Switch Component Styles
   =================================== */
/* Enhanced Theme Switch with Better Theme Integration */
.switch {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 24px;
}
.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}
.switch input:checked + .slider {
  background-color: var(--text-color);
}
.switch input:checked + .slider:before {
  transform: translateX(20px);
  background-color: transparent;
  border-radius: 50%;
  border-top-color: transparent;
  border-left-color: transparent;
  border-right-color: transparent;
  box-shadow: inset -5px -3px 0 var(--bg-color);
}
.switch .slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--hover-bg-color);
  transition: all var(--transition-slow) var(--ease-out);
  border-radius: 20px;
  border: 1px solid var(--border-color);
  box-shadow: 0 0 0.25em var(--shadow-color-d), 0.2px 0.2em 24px 0 var(--shadow-color-l);
}
.switch .slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 4px;
  bottom: 4px;
  background-color: oklch(0.85 0.15 85); /* Golden sun color */
  transition: all var(--transition-slow) var(--ease-out);
  border-radius: 50%;
  text-align: center;
  line-height: 16px;
  box-shadow: 0 0 0.25em var(--shadow-color-d);
}

/* ===================================
   Video Player Component Styles
   =================================== */
/* Loading Animation Enhancements */
#loadingAnimation {
  animation: fadeIn 0.3s var(--ease-out);
}
#loadingAnimation .spinner-border {
  animation: spin 1s linear infinite;
}

/* ===================================
   Alert Component Styles
   =================================== */
/* Alert Enhancements */
.alert {
  border: 1px solid var(--border-color);
  background-color: var(--card-bg-color);
  color: var(--text-color);
  transition: all var(--transition-normal) var(--ease-out);
}

/* ===================================
   Utility Classes - Bootstrap Compatible
   =================================== */
/* ===== SPACING UTILITIES ===== */
/* Margin utilities using design tokens */
.m-0 {
  margin: 0 !important;
}

.m-1 {
  margin: var(--space-xs) !important;
}

.m-2 {
  margin: var(--space-sm) !important;
}

.m-3 {
  margin: var(--space-md) !important;
}

.m-4 {
  margin: var(--space-lg) !important;
}

.m-5 {
  margin: var(--space-xl) !important;
}

/* Margin top */
.mt-0 {
  margin-top: 0 !important;
}

.mt-1 {
  margin-top: var(--space-xs) !important;
}

.mt-2 {
  margin-top: var(--space-sm) !important;
}

.mt-3 {
  margin-top: var(--space-md) !important;
}

.mt-4 {
  margin-top: var(--space-lg) !important;
}

.mt-5 {
  margin-top: var(--space-xl) !important;
}

/* Margin bottom */
.mb-0 {
  margin-bottom: 0 !important;
}

.mb-1 {
  margin-bottom: var(--space-xs) !important;
}

.mb-2 {
  margin-bottom: var(--space-sm) !important;
}

.mb-3 {
  margin-bottom: var(--space-md) !important;
}

.mb-4 {
  margin-bottom: var(--space-lg) !important;
}

.mb-5 {
  margin-bottom: var(--space-xl) !important;
}

/* Padding utilities */
.p-0 {
  padding: 0 !important;
}

.p-1 {
  padding: var(--space-xs) !important;
}

.p-2 {
  padding: var(--space-sm) !important;
}

.p-3 {
  padding: var(--space-md) !important;
}

.p-4 {
  padding: var(--space-lg) !important;
}

.p-5 {
  padding: var(--space-xl) !important;
}

/* Padding top */
.pt-0 {
  padding-top: 0 !important;
}

.pt-1 {
  padding-top: var(--space-xs) !important;
}

.pt-2 {
  padding-top: var(--space-sm) !important;
}

.pt-3 {
  padding-top: var(--space-md) !important;
}

.pt-4 {
  padding-top: var(--space-lg) !important;
}

.pt-5 {
  padding-top: var(--space-xl) !important;
}

/* Padding bottom */
.pb-0 {
  padding-bottom: 0 !important;
}

.pb-1 {
  padding-bottom: var(--space-xs) !important;
}

.pb-2 {
  padding-bottom: var(--space-sm) !important;
}

.pb-3 {
  padding-bottom: var(--space-md) !important;
}

.pb-4 {
  padding-bottom: var(--space-lg) !important;
}

.pb-5 {
  padding-bottom: var(--space-xl) !important;
}

/* ===== DISPLAY UTILITIES ===== */
.d-none {
  display: none !important;
}

.d-inline {
  display: inline !important;
}

.d-inline-block {
  display: inline-block !important;
}

.d-block {
  display: block !important;
}

.d-flex {
  display: flex !important;
}

.d-inline-flex {
  display: inline-flex !important;
}

.d-grid {
  display: grid !important;
}

/* ===== FLEXBOX UTILITIES ===== */
.flex-row {
  flex-direction: row !important;
}

.flex-column {
  flex-direction: column !important;
}

.flex-wrap {
  flex-wrap: wrap !important;
}

.flex-nowrap {
  flex-wrap: nowrap !important;
}

.justify-content-start {
  justify-content: flex-start !important;
}

.justify-content-end {
  justify-content: flex-end !important;
}

.justify-content-center {
  justify-content: center !important;
}

.justify-content-between {
  justify-content: space-between !important;
}

.justify-content-around {
  justify-content: space-around !important;
}

.justify-content-evenly {
  justify-content: space-evenly !important;
}

.align-items-start {
  align-items: flex-start !important;
}

.align-items-end {
  align-items: flex-end !important;
}

.align-items-center {
  align-items: center !important;
}

.align-items-baseline {
  align-items: baseline !important;
}

.align-items-stretch {
  align-items: stretch !important;
}

/* ===== TEXT UTILITIES ===== */
.text-start {
  text-align: start !important;
}

.text-end {
  text-align: end !important;
}

.text-center {
  text-align: center !important;
}

.text-justify {
  text-align: justify !important;
}

.text-lowercase {
  text-transform: lowercase !important;
}

.text-uppercase {
  text-transform: uppercase !important;
}

.text-capitalize {
  text-transform: capitalize !important;
}

.fw-light {
  font-weight: var(--font-weight-light) !important;
}

.fw-normal {
  font-weight: var(--font-weight-normal) !important;
}

.fw-medium {
  font-weight: var(--font-weight-medium) !important;
}

.fw-semibold {
  font-weight: var(--font-weight-semibold) !important;
}

.fw-bold {
  font-weight: var(--font-weight-bold) !important;
}

.fs-1 {
  font-size: var(--font-size-5xl) !important;
}

.fs-2 {
  font-size: var(--font-size-4xl) !important;
}

.fs-3 {
  font-size: var(--font-size-3xl) !important;
}

.fs-4 {
  font-size: var(--font-size-2xl) !important;
}

.fs-5 {
  font-size: var(--font-size-xl) !important;
}

.fs-6 {
  font-size: var(--font-size-lg) !important;
}

/* ===== COLOR UTILITIES ===== */
.text-primary {
  color: var(--accent-primary) !important;
}

.text-secondary {
  color: var(--color-gray-600) !important;
}

.text-success {
  color: var(--color-success) !important;
}

.text-warning {
  color: var(--color-warning) !important;
}

.text-danger {
  color: var(--color-error) !important;
}

.text-info {
  color: var(--color-info) !important;
}

.text-light {
  color: var(--color-gray-300) !important;
}

.text-dark {
  color: var(--color-gray-800) !important;
}

.text-muted {
  color: var(--muted-text-color) !important;
}

.text-white {
  color: var(--color-white) !important;
}

.text-black {
  color: var(--color-black) !important;
}

/* Background colors */
.bg-primary {
  background-color: var(--accent-primary) !important;
}

.bg-secondary {
  background-color: var(--color-gray-600) !important;
}

.bg-success {
  background-color: var(--color-success) !important;
}

.bg-warning {
  background-color: var(--color-warning) !important;
}

.bg-danger {
  background-color: var(--color-error) !important;
}

.bg-info {
  background-color: var(--color-info) !important;
}

.bg-light {
  background-color: var(--color-gray-100) !important;
}

.bg-dark {
  background-color: var(--color-gray-800) !important;
}

.bg-white {
  background-color: var(--color-white) !important;
}

.bg-transparent {
  background-color: transparent !important;
}

/* ===== BORDER UTILITIES ===== */
.border {
  border: 1px solid var(--border-color) !important;
}

.border-0 {
  border: 0 !important;
}

.border-top {
  border-top: 1px solid var(--border-color) !important;
}

.border-end {
  border-right: 1px solid var(--border-color) !important;
}

.border-bottom {
  border-bottom: 1px solid var(--border-color) !important;
}

.border-start {
  border-left: 1px solid var(--border-color) !important;
}

.rounded {
  border-radius: var(--radius-md) !important;
}

.rounded-0 {
  border-radius: 0 !important;
}

.rounded-1 {
  border-radius: var(--radius-sm) !important;
}

.rounded-2 {
  border-radius: var(--radius-md) !important;
}

.rounded-3 {
  border-radius: var(--radius-lg) !important;
}

.rounded-4 {
  border-radius: var(--radius-xl) !important;
}

.rounded-5 {
  border-radius: var(--radius-2xl) !important;
}

.rounded-circle {
  border-radius: var(--radius-full) !important;
}

.rounded-pill {
  border-radius: 50rem !important;
}

/* ===== SHADOW UTILITIES ===== */
.shadow-none {
  box-shadow: none !important;
}

.shadow-sm {
  box-shadow: var(--shadow-sm) !important;
}

.shadow {
  box-shadow: var(--shadow-md) !important;
}

.shadow-lg {
  box-shadow: var(--shadow-lg) !important;
}

.shadow-xl {
  box-shadow: var(--shadow-xl) !important;
}

/* ===== POSITION UTILITIES ===== */
.position-static {
  position: static !important;
}

.position-relative {
  position: relative !important;
}

.position-absolute {
  position: absolute !important;
}

.position-fixed {
  position: fixed !important;
}

.position-sticky {
  position: sticky !important;
}

/* ===== OVERFLOW UTILITIES ===== */
.overflow-auto {
  overflow: auto !important;
}

.overflow-hidden {
  overflow: hidden !important;
}

.overflow-visible {
  overflow: visible !important;
}

.overflow-scroll {
  overflow: scroll !important;
}

/* ===== ANIMATION UTILITIES ===== */
.fade-in {
  animation: var(--animation-fade-in);
}

.slide-up {
  animation: var(--animation-slide-up);
}

.scale-in {
  animation: var(--animation-scale-in);
}

/* Transition utilities */
.transition-all {
  transition: all var(--transition-normal) var(--ease-out) !important;
}

.transition-colors {
  transition: color var(--transition-normal) var(--ease-out), background-color var(--transition-normal) var(--ease-out), border-color var(--transition-normal) var(--ease-out) !important;
}

.transition-opacity {
  transition: opacity var(--transition-normal) var(--ease-out) !important;
}

.transition-transform {
  transition: transform var(--transition-normal) var(--ease-out) !important;
}

/* ===== RESPONSIVE UTILITIES ===== */
/* Hide/show on different screen sizes */
@media (max-width: 575.98px) {
  .d-sm-none {
    display: none !important;
  }
}
@media (min-width: 576px) {
  .d-sm-block {
    display: block !important;
  }
  .d-sm-flex {
    display: flex !important;
  }
}
@media (max-width: 767.98px) {
  .d-md-none {
    display: none !important;
  }
}
@media (min-width: 768px) {
  .d-md-block {
    display: block !important;
  }
  .d-md-flex {
    display: flex !important;
  }
}
@media (max-width: 991.98px) {
  .d-lg-none {
    display: none !important;
  }
}
@media (min-width: 992px) {
  .d-lg-block {
    display: block !important;
  }
  .d-lg-flex {
    display: flex !important;
  }
}
/* ===== ACCESSIBILITY UTILITIES ===== */
.visually-hidden {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

.visually-hidden-focusable:focus,
.visually-hidden-focusable:focus-within {
  position: static !important;
  width: auto !important;
  height: auto !important;
  overflow: visible !important;
  clip: auto !important;
  white-space: normal !important;
}

/* Focus utilities */
.focus-ring:focus {
  outline: 0;
  box-shadow: var(--focus-ring);
}

/* ===== INTERACTION UTILITIES ===== */
.user-select-all {
  -webkit-user-select: all !important;
     -moz-user-select: all !important;
          user-select: all !important;
}

.user-select-auto {
  -webkit-user-select: auto !important;
     -moz-user-select: auto !important;
          user-select: auto !important;
}

.user-select-none {
  -webkit-user-select: none !important;
     -moz-user-select: none !important;
          user-select: none !important;
}

.pointer-events-none {
  pointer-events: none !important;
}

.pointer-events-auto {
  pointer-events: auto !important;
}

.cursor-pointer {
  cursor: pointer !important;
}

.cursor-default {
  cursor: default !important;
}

.cursor-not-allowed {
  cursor: not-allowed !important;
}

/* ===================================
   Animation Utilities
   =================================== */
/* Keyframe Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
/* ===================================
   Enhanced Responsive Utilities
   =================================== */
/* ===== MOBILE FIRST BREAKPOINTS ===== */
/* Extra small devices (portrait phones, less than 576px) */
@media (max-width: 575.98px) {
  :root {
    /* Adjust spacing for mobile */
    --space-lg: 1rem;
    --space-xl: 1.5rem;
    --space-2xl: 2rem;
    /* Adjust typography for mobile */
    --font-size-4xl: 1.875rem; /* Smaller h1 on mobile */
    --font-size-3xl: 1.5rem;
    --font-size-2xl: 1.25rem;
  }
  /* Typography adjustments */
  h1 {
    font-size: var(--font-size-4xl);
    line-height: var(--line-height-tight);
  }
  h2, h3, h4 {
    font-size: var(--font-size-2xl);
    line-height: var(--line-height-normal);
  }
  /* Container adjustments */
  .container-fluid {
    padding-left: var(--space-md);
    padding-right: var(--space-md);
  }
  /* Navigation adjustments */
  .navbar {
    padding: var(--space-sm) var(--space-md);
  }
  .navbar-brand img {
    height: 2rem;
  }
  /* Card adjustments */
  .card {
    margin-bottom: var(--space-md);
  }
  /* Button adjustments */
  .btn {
    padding: var(--space-sm) var(--space-md);
    font-size: var(--font-size-sm);
  }
}
/* Small devices (landscape phones, 576px and up) */
@media (min-width: 576px) {
  .navbar-nav {
    flex-direction: row;
    gap: var(--space-sm);
  }
}
/* Medium devices (tablets, 768px and up) */
@media (min-width: 768px) {
  :root {
    /* Restore larger spacing on tablets+ */
    --navbar-padding-x: var(--space-xl);
  }
  .navbar {
    padding: var(--space-md) var(--space-xl);
  }
  .navbar-nav {
    gap: var(--space-md);
  }
  /* Grid improvements */
  .row {
    margin-left: calc(var(--space-md) * -0.5);
    margin-right: calc(var(--space-md) * -0.5);
  }
  .col, [class*=col-] {
    padding-left: calc(var(--space-md) * 0.5);
    padding-right: calc(var(--space-md) * 0.5);
  }
}
/* Large devices (desktops, 992px and up) */
@media (min-width: 992px) {
  /* Enhanced hover effects for desktop */
  #videoContainer .card {
    transition: transform var(--transition-normal) var(--ease-out), box-shadow var(--transition-normal) var(--ease-out);
  }
  #videoContainer .card:hover {
    transform: translateY(-4px);
    box-shadow: var(--card-hover-shadow);
  }
  /* Better button hover effects */
  .btn {
    transition: all var(--transition-normal) var(--ease-out);
  }
  .btn:hover {
    transform: translateY(-1px);
  }
  /* Navigation enhancements */
  .nav-link {
    transition: all var(--transition-normal) var(--ease-out);
  }
  .nav-link:hover {
    transform: translateY(-1px);
  }
}
/* Extra large devices (large desktops, 1200px and up) */
@media (min-width: 1200px) {
  :root {
    /* Larger spacing for large screens */
    --space-4xl: 8rem;
  }
  /* Enhanced typography scale */
  h1 {
    font-size: var(--font-size-5xl);
  }
}
/* Extra extra large devices (larger desktops, 1400px and up) */
@media (min-width: 1400px) {
  .container-fluid {
    max-width: 1320px;
    margin: 0 auto;
  }
}/*# sourceMappingURL=main.css.map */