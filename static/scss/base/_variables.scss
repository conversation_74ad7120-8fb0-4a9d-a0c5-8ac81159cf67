/* ===================================
   Enhanced CSS Custom Properties with OKLCH Colors
   =================================== */

/* OKLCH Color System
   OKLCH provides better perceptual uniformity and wider color gamut
   Format: oklch(lightness chroma hue / alpha)
   - Lightness: 0-1 (0 = black, 1 = white)
   - Chroma: 0+ (0 = grayscale, higher = more saturated)
   - Hue: 0-360 degrees
*/

:root {
  /* ===== DESIGN TOKENS - SPACING ===== */
  --space-xs: 0.25rem;    /* 4px */
  --space-sm: 0.5rem;     /* 8px */
  --space-md: 1rem;       /* 16px */
  --space-lg: 1.5rem;     /* 24px */
  --space-xl: 2rem;       /* 32px */
  --space-2xl: 3rem;      /* 48px */
  --space-3xl: 4rem;      /* 64px */
  --space-4xl: 6rem;      /* 96px */

  /* ===== DESIGN TOKENS - BORDER RADIUS ===== */
  --radius-none: 0;
  --radius-sm: 0.125rem;  /* 2px */
  --radius-md: 0.25rem;   /* 4px */
  --radius-lg: 0.5rem;    /* 8px */
  --radius-xl: 0.75rem;   /* 12px */
  --radius-2xl: 1rem;     /* 16px */
  --radius-full: 9999px;

  /* ===== DESIGN TOKENS - SHADOWS ===== */
  --shadow-sm: 0 1px 2px 0 oklch(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px oklch(0 0 0 / 0.1), 0 2px 4px -2px oklch(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px oklch(0 0 0 / 0.1), 0 4px 6px -4px oklch(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px oklch(0 0 0 / 0.1), 0 8px 10px -6px oklch(0 0 0 / 0.1);

  /* ===== PRIMARY COLORS ===== */
  /* Red accent colors using OKLCH */
  --accent-color: oklch(0.55 0.22 25); /* Bright red #ff0800 equivalent */
  --accent-hover-color: oklch(0.45 0.20 25); /* Darker red #cc0600 equivalent */
  --accent-dark-color: oklch(0.60 0.18 25); /* Dark theme red #ff4444 equivalent */
  --accent-dark-hover-color: oklch(0.70 0.16 25); /* Dark theme hover #ff6666 equivalent */

  /* ===== NEUTRAL COLORS ===== */
  /* Pure whites and blacks */
  --color-white: oklch(1.0 0 0); /* Pure white */
  --color-black: oklch(0.0 0 0); /* Pure black */

  /* Grays with slight warm tint for better visual harmony */
  --color-gray-50: oklch(0.98 0.005 85);
  --color-gray-100: oklch(0.95 0.005 85);
  --color-gray-200: oklch(0.90 0.005 85);
  --color-gray-300: oklch(0.80 0.005 85);
  --color-gray-400: oklch(0.65 0.005 85);
  --color-gray-500: oklch(0.50 0.005 85);
  --color-gray-600: oklch(0.40 0.005 85);
  --color-gray-700: oklch(0.30 0.005 85);
  --color-gray-800: oklch(0.20 0.005 85);
  --color-gray-900: oklch(0.10 0.005 85);

  /* ===== SEMANTIC COLORS ===== */
  /* Success colors */
  --color-success: oklch(0.65 0.15 145); /* Green */
  --color-success-hover: oklch(0.55 0.15 145);

  /* Warning/favorite colors */
  --color-warning: oklch(0.80 0.15 85); /* Golden yellow #ffc107 equivalent */
  --color-warning-hover: oklch(0.85 0.15 85); /* Lighter golden #ffcd39 equivalent */

  /* Error colors */
  --color-error: oklch(0.55 0.22 25); /* Red - matches accent */
  --color-error-hover: oklch(0.45 0.20 25);

  /* Info colors */
  --color-info: oklch(0.60 0.15 250); /* Blue */
  --color-info-hover: oklch(0.50 0.15 250);

  /* ===== ALPHA/TRANSPARENCY VALUES ===== */
  --alpha-light: 0.05;
  --alpha-medium: 0.125;
  --alpha-heavy: 0.25;
  --alpha-backdrop: 0.95;
  
  /* ===== ANIMATION AND TRANSITION VARIABLES ===== */
  /* Transition durations */
  --transition-fast: 0.15s;
  --transition-normal: 0.25s;
  --transition-slow: 0.35s;
  --transition-slower: 0.5s;

  /* Easing functions - optimized for better UX */
  --ease-out: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --ease-in-out: cubic-bezier(0.645, 0.045, 0.355, 1);
  --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --ease-smooth: cubic-bezier(0.4, 0, 0.2, 1);

  /* Animation delays for staggered effects */
  --stagger-delay-1: 0.05s;
  --stagger-delay-2: 0.1s;
  --stagger-delay-3: 0.15s;
  --stagger-delay-4: 0.2s;
  --stagger-delay-5: 0.25s;
  --stagger-delay-6: 0.3s;

  /* ===== TYPOGRAPHY SCALE ===== */
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */
  --font-size-4xl: 2.25rem;   /* 36px */
  --font-size-5xl: 3rem;      /* 48px */
  --font-size-6xl: 3.75rem;   /* 60px */

  /* Line heights */
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;

  /* Font weights */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;

  /* ===== Z-INDEX SCALE ===== */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;

  /* Bootstrap compatibility */
  --bs-white: var(--color-white);
  --bs-black: var(--color-black);
}

/* ===== LIGHT THEME VARIABLES ===== */
:root,
[data-theme="light"] {
  /* Background colors */
  --bg-color: var(--color-white);
  --card-bg-color: var(--color-white);
  --navbar-bg-color: oklch(from var(--color-white) l c h / var(--alpha-backdrop));
  
  /* Text colors */
  --text-color: var(--color-black);
  --muted-text-color: var(--color-gray-500);
  
  /* Interactive colors */
  --accent-primary: var(--accent-color);
  --accent-primary-hover: var(--accent-hover-color);
  
  /* Border and shadow colors */
  --border-color: oklch(from var(--color-black) l c h / var(--alpha-medium));
  --hover-bg-color: oklch(from var(--color-black) l c h / var(--alpha-light));
  --shadow-color-d: oklch(from var(--color-black) l c h / 0.051);
  --shadow-color-l: oklch(from var(--color-white) l c h / var(--alpha-light));
  
  /* Focus ring colors */
  --focus-ring: oklch(from var(--accent-color) l c h / var(--alpha-heavy));
}

/* ===== DARK THEME VARIABLES ===== */
[data-theme="dark"],
body.dark {
  /* Background colors */
  --bg-color: var(--color-black);
  --card-bg-color: var(--color-gray-900);
  --navbar-bg-color: oklch(from var(--color-black) l c h / var(--alpha-backdrop));
  
  /* Text colors */
  --text-color: var(--color-white);
  --muted-text-color: var(--color-gray-400);
  
  /* Interactive colors */
  --accent-primary: var(--accent-dark-color);
  --accent-primary-hover: var(--accent-dark-hover-color);
  
  /* Border and shadow colors */
  --border-color: oklch(from var(--color-white) l c h / var(--alpha-medium));
  --hover-bg-color: oklch(from var(--color-white) l c h / var(--alpha-light));
  --shadow-color-d: oklch(from var(--color-white) l c h / 0.051);
  --shadow-color-l: oklch(from var(--color-black) l c h / var(--alpha-light));
  
  /* Focus ring colors */
  --focus-ring: oklch(from var(--accent-dark-color) l c h / var(--alpha-heavy));
}
