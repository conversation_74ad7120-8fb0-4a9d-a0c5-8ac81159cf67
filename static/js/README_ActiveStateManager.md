# Active State Manager

O Active State Manager é um sistema completo para gerenciar classes ativas em elementos HTML com cache persistente usando localStorage.

## Funcionalidades

### 1. Gerenciamento de Nav Links
- Adiciona classe `active` aos links de navegação
- Detecta automaticamente a página atual
- Persiste o estado no localStorage

### 2. Gerenciamento de Botões de Idioma
- Adiciona classes `active` e `btn-active` aos botões de idioma
- Persiste a seleção de idioma no localStorage
- Restaura o estado ao carregar a página

### 3. Gerenciamento de Botões de Tema
- Adiciona classes `active` e `btn-active` aos botões de tema
- Persiste a seleção de tema no localStorage
- Padrão para tema "light" se não houver cache

## Como Usar

### Inicialização Automática
O sistema é inicializado automaticamente quando o DOM está pronto através do `app.js`.

### API Pública

#### Navegação
```javascript
// Definir link ativo manualmente
ActiveStateManager.nav.setActive('index.html');

// Definir baseado na página atual
ActiveStateManager.nav.setActiveFromCurrentPage();

// Restaurar do cache
ActiveStateManager.nav.restoreFromCache();
```

#### Idiomas
```javascript
// Definir idioma ativo
ActiveStateManager.language.setActive('en');

// Obter idioma ativo
const currentLang = ActiveStateManager.language.getActive();

// Restaurar do cache
ActiveStateManager.language.restoreFromCache();
```

#### Temas
```javascript
// Definir tema ativo
ActiveStateManager.theme.setActive('dark');

// Obter tema ativo
const currentTheme = ActiveStateManager.theme.getActive();

// Restaurar do cache
ActiveStateManager.theme.restoreFromCache();
```

#### Cache
```javascript
// Definir valor no cache
ActiveStateManager.cache.set('chave', 'valor');

// Obter valor do cache
const valor = ActiveStateManager.cache.get('chave');

// Remover valor do cache
ActiveStateManager.cache.remove('chave');
```

## Configuração

### Seletores CSS
```javascript
const ActiveStateConfig = {
  selectors: {
    navLinks: '.nav-link',
    languageButtons: '[data-language]',
    themeButtons: '[data-theme-toggle]'
  }
};
```

### Classes CSS
```javascript
const ActiveStateConfig = {
  classes: {
    active: 'active',
    btnActive: 'btn-active'
  }
};
```

### Chaves do Cache
```javascript
const ActiveStateConfig = {
  cache: {
    navKey: 'activeNavLink',
    languageKey: 'activeLanguage',
    themeKey: 'activeTheme'
  }
};
```

## Estrutura HTML Esperada

### Links de Navegação
```html
<a class="nav-link" href="./index.html">Homepage</a>
<a class="nav-link" href="./main.html">Videos</a>
<a class="nav-link" href="./about.html">About</a>
```

### Botões de Idioma
```html
<button type="button" class="btn" data-language="en">English</button>
<button type="button" class="btn" data-language="br">Português</button>
<button type="button" class="btn" data-language="jp">日本語</button>
```

### Botões de Tema
```html
<button type="button" class="btn" data-theme-toggle data-theme-value="light">Light</button>
<button type="button" class="btn" data-theme-toggle data-theme-value="dark">Dark</button>
```

## Estilos CSS

O sistema adiciona as seguintes classes:

- `.active` - Para links de navegação e estado geral ativo
- `.btn-active` - Para botões ativos (idioma e tema)

### Estilos Incluídos
```scss
.active {
  color: var(--accent-primary);
}

.btn-active {
  background-color: var(--accent-primary) !important;
  color: var(--text-on-accent) !important;
  border-color: var(--accent-primary) !important;
}

.nav-link.active {
  color: var(--accent-primary) !important;
  font-weight: 600;
  position: relative;
}

.nav-link.active::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 2px;
  background-color: var(--accent-primary);
  border-radius: 1px;
}
```

## Eventos

O sistema adiciona automaticamente event listeners para:
- Cliques em links de navegação
- Cliques em botões de idioma
- Cliques em botões de tema

## Compatibilidade

- Funciona com localStorage (fallback gracioso se não disponível)
- Compatible com todos os navegadores modernos
- Não requer dependências externas

## Debugging

Para ativar logs de debug, defina `AppConfig.debug = true` no `app.js`.
