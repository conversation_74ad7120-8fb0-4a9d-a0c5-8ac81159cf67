/**
 * Include Manager - Sistema para importar partials HTML usando atributos
 * Uso: <div data-include="header.html"></div>
 */

class IncludeManager {
  constructor() {
    this.cache = new Map();
    this.loadingPromises = new Map();
  }

  /**
   * Inicializa o sistema de includes
   */
  async init() {
    try {
      await this.loadIncludes();
      console.log('✅ Include Manager: Todos os partials foram carregados');
    } catch (error) {
      console.error('❌ Include Manager: Erro ao carregar partials:', error);
    }
  }

  /**
   * Carrega todos os elementos com data-include
   */
  async loadIncludes() {
    const includeElements = document.querySelectorAll('[data-include]');
    
    if (includeElements.length === 0) {
      return;
    }

    const loadPromises = Array.from(includeElements).map(element => 
      this.loadInclude(element)
    );

    await Promise.all(loadPromises);
  }

  /**
   * Carrega um include específico
   * @param {HTMLElement} element - Elemento com data-include
   */
  async loadInclude(element) {
    const includePath = element.getAttribute('data-include');
    
    if (!includePath) {
      console.warn('Include Manager: data-include vazio encontrado');
      return;
    }

    try {
      // Mostra indicador de carregamento
      element.innerHTML = '<div class="text-center p-2"><small>Carregando...</small></div>';
      
      const content = await this.fetchInclude(includePath);
      
      // Substitui o conteúdo
      element.innerHTML = content;
      
      // Remove o atributo data-include para evitar reprocessamento
      element.removeAttribute('data-include');
      
      // Dispara evento personalizado para indicar que o include foi carregado
      element.dispatchEvent(new CustomEvent('includeLoaded', {
        detail: { path: includePath }
      }));
      
    } catch (error) {
      console.error(`Include Manager: Erro ao carregar ${includePath}:`, error);
      element.innerHTML = `<div class="alert alert-warning p-2"><small>Erro ao carregar ${includePath}</small></div>`;
    }
  }

  /**
   * Busca o conteúdo do arquivo include
   * @param {string} path - Caminho do arquivo
   * @returns {Promise<string>} - Conteúdo do arquivo
   */
  async fetchInclude(path) {
    // Verifica se já está sendo carregado
    if (this.loadingPromises.has(path)) {
      return await this.loadingPromises.get(path);
    }

    // Verifica cache
    if (this.cache.has(path)) {
      return this.cache.get(path);
    }

    // Cria promise de carregamento
    const loadingPromise = this.doFetch(path);
    this.loadingPromises.set(path, loadingPromise);

    try {
      const content = await loadingPromise;
      this.cache.set(path, content);
      return content;
    } finally {
      this.loadingPromises.delete(path);
    }
  }

  /**
   * Executa o fetch do arquivo
   * @param {string} path - Caminho do arquivo
   * @returns {Promise<string>} - Conteúdo do arquivo
   */
  async doFetch(path) {
    const response = await fetch(path);
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    return await response.text();
  }

  /**
   * Limpa o cache (útil para desenvolvimento)
   */
  clearCache() {
    this.cache.clear();
    console.log('Include Manager: Cache limpo');
  }

  /**
   * Recarrega todos os includes na página
   */
  async reload() {
    this.clearCache();
    await this.loadIncludes();
  }
}

// Instância global
const includeManager = new IncludeManager();

// Auto-inicialização quando o DOM estiver pronto
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => includeManager.init());
} else {
  includeManager.init();
}

// Exporta para uso global
window.includeManager = includeManager;
