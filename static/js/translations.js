/* The `translations` object in the JavaScript code snippet contains translations for different
languages. Each language is represented by a key (en for English, br for Portuguese, jp for
Japanese) and has an object with key-value pairs for various phrases and messages commonly found on
a website. */
const translations = {
  "en-US": {
    title: {
      index: "iSHIUBA",
      main: "Videos",
      about: "About",
    },
    ishiuba: "iSHIUBA",
    videos: "Videos",
    about: "About",
    greeting: "Hello, world!",
    mainMessage:
      "The website is still under construction, but you can check out some of my videos, my about page, <br> or my social networks that are present in the website's footer.",
    highlight: "Highlights",
    footer: "All rights reserved.",
    videosTitle: "Videos",
    videosDescription:
      "Explore my music playlists and videos",
    aboutTitle: "About",
    aboutMessage:
      "I made this website for fun. Hope you enjoy coz I'm about to make it better and better!<br/> Thank you for visiting!",
    Homepage: "Homepage",
    Videos: "Videos",
    About: "About",
    searchPlaylists: "Search playlists...",
    loading: "Loading playlists...",
    noPlaylistsFound: "No playlists found.",
    refreshPlaylists: "Refresh playlists",
    toggleView: "Toggle view layout",
    addToFavorites: "Add to favorites",
    removeFromFavorites: "Remove from favorites",
    openInYoutube: "Open in YouTube",
    loadPlayer: "Load Player",
    clickToLoad: "Click to load the video player",
    errorLoadingPlaylists: "Failed to load playlists",
    retryButton: "Retry",
  },
  "pt-BR": {
    title: {
      index: "iSHIUBA",
      main: "Vídeos",
      about: "Sobre",
    },
    ishiuba: "iSHIUBA",
    videos: "Vídeos",
    about: "Sobre",
    greeting: "Olá, mundo!",
    mainMessage:
      "O site ainda está em construção, mas você pode conferir alguns dos meus vídeos, minha página sobre <br> ou minhas redes sociais que estão presentes no rodapé do site.",
    highlight: "Destaques",
    footer: "Todos os direitos reservados.",
    videosTitle: "Vídeos",
    videosDescription:
      "Explore minhas playlists e vídeos musicais",
    aboutTitle: "Sobre",
    aboutMessage:
      "Eu fiz este site por diversão. Espero que você goste, pois estou prestes a melhorá-lo cada vez mais!<br/> Obrigado pela visita!",
    Homepage: "Página Inicial",
    Videos: "Vídeos",
    About: "Sobre",
    searchPlaylists: "Buscar playlists...",
    loading: "Carregando playlists...",
    noPlaylistsFound: "Nenhuma playlist encontrada.",
    refreshPlaylists: "Atualizar playlists",
    toggleView: "Alternar visualização",
    addToFavorites: "Adicionar aos favoritos",
    removeFromFavorites: "Remover dos favoritos",
    openInYoutube: "Abrir no YouTube",
    loadPlayer: "Carregar Vídeo",
    clickToLoad: "Clique para carregar o player de vídeo",
    errorLoadingPlaylists: "Falha ao carregar playlists",
    retryButton: "Tentar novamente",
  },
  "jp-JP": {
    title: {
      index: "iSHIUBA",
      main: "ビデオ",
      about: "概要",
    },
    ishiuba: "アイシウバ",
    videos: "ビデオ",
    about: "概要",
    greeting: "こんにちは、世界！",
    mainMessage:
      "ウェブサイトはまだ建設中ですが、いくつかのビデオ、概要ページ、<br>またはフッターにあるソーシャルネットワークをチェックできます。",
    highlight: "ハイライト",
    footer: "全著作権所有。",
    videosTitle: "ビデオ",
    videosDescription:
      "私の音楽プレイリストとビデオを探索してください",
    aboutTitle: "概要",
    aboutMessage:
      "私はこのウェブサイトを楽しみのために作りました。気に入ってもらえると嬉しいです。<br/>どんどん改善していくつもりです！訪問してくれてありがとうございます！",
    Homepage: "ホームページ",
    Videos: "ビデオ",
    About: "概要",
    searchPlaylists: "プレイリストを検索...",
    loading: "プレイリストを読み込み中...",
    noPlaylistsFound: "プレイリストが見つかりません。",
    refreshPlaylists: "プレイリストを更新",
    toggleView: "表示レイアウトを切り替え",
    addToFavorites: "お気に入りに追加",
    removeFromFavorites: "お気に入りから削除",
    openInYoutube: "YouTubeで開く",
    loadPlayer: "ビデオを読み込む",
    clickToLoad: "ビデオプレーヤーを読み込むにはクリックしてください",
    errorLoadingPlaylists: "プレイリストの読み込みに失敗しました",
    retryButton: "再試行",
  },
};
