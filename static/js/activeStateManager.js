/**
 * Active State Manager
 * Manages active classes for navigation links, language buttons, and theme buttons
 * Includes localStorage cache to persist active states across page loads
 */

/**
 * Active State Manager Configuration
 */
const ActiveStateConfig = {
  cache: {
    navKey: 'activeNavLink',
    languageKey: 'activeLanguage',
    themeKey: 'activeTheme'
  },
  selectors: {
    navLinks: '.nav-link',
    languageButtons: '[data-language]',
    themeButtons: '[data-theme-toggle]'
  },
  classes: {
    active: 'active',
    btnActive: 'btn-active'
  }
};

/**
 * Cache utility functions
 */
const CacheManager = {
  /**
   * Sets a value in localStorage
   * @param {string} key - The cache key
   * @param {string} value - The value to cache
   */
  set(key, value) {
    try {
      localStorage.setItem(key, value);
    } catch (error) {
      console.warn('Failed to set cache:', error);
    }
  },

  /**
   * Gets a value from localStorage
   * @param {string} key - The cache key
   * @returns {string|null} - The cached value or null
   */
  get(key) {
    try {
      return localStorage.getItem(key);
    } catch (error) {
      console.warn('Failed to get cache:', error);
      return null;
    }
  },

  /**
   * Removes a value from localStorage
   * @param {string} key - The cache key
   */
  remove(key) {
    try {
      localStorage.removeItem(key);
    } catch (error) {
      console.warn('Failed to remove cache:', error);
    }
  }
};

/**
 * Navigation Links Active State Manager
 */
const NavActiveManager = {
  /**
   * Sets active class for navigation link
   * @param {string} href - The href of the link to activate
   */
  setActive(href) {
    const navLinks = document.querySelectorAll(ActiveStateConfig.selectors.navLinks);
    
    navLinks.forEach(link => {
      link.classList.remove(ActiveStateConfig.classes.active);
      
      // Check if this link matches the href
      if (link.getAttribute('href') === href || 
          link.getAttribute('href') === `./${href}` ||
          (href.includes(link.getAttribute('href').replace('./', '')) && 
           link.getAttribute('href') !== './index.html')) {
        link.classList.add(ActiveStateConfig.classes.active);
        CacheManager.set(ActiveStateConfig.cache.navKey, href);
      }
    });
  },

  /**
   * Sets active class based on current page
   */
  setActiveFromCurrentPage() {
    const currentPage = window.location.pathname.split('/').pop() || 'index.html';
    this.setActive(currentPage);
  },

  /**
   * Restores active state from cache
   */
  restoreFromCache() {
    const cachedHref = CacheManager.get(ActiveStateConfig.cache.navKey);
    if (cachedHref) {
      this.setActive(cachedHref);
    } else {
      this.setActiveFromCurrentPage();
    }
  },

  /**
   * Initializes navigation active state management
   */
  init() {
    // Set active based on current page on load
    this.setActiveFromCurrentPage();
    
    // Add click event listeners to nav links
    const navLinks = document.querySelectorAll(ActiveStateConfig.selectors.navLinks);
    navLinks.forEach(link => {
      link.addEventListener('click', (e) => {
        const href = e.target.getAttribute('href');
        if (href) {
          this.setActive(href);
        }
      });
    });
  }
};

/**
 * Language Buttons Active State Manager
 */
const LanguageActiveManager = {
  /**
   * Sets active class for language button
   * @param {string} language - The language code to activate
   */
  setActive(language) {
    const languageButtons = document.querySelectorAll(ActiveStateConfig.selectors.languageButtons);
    
    languageButtons.forEach(button => {
      button.classList.remove(ActiveStateConfig.classes.active, ActiveStateConfig.classes.btnActive);
      
      if (button.getAttribute('data-language') === language) {
        button.classList.add(ActiveStateConfig.classes.active, ActiveStateConfig.classes.btnActive);
        CacheManager.set(ActiveStateConfig.cache.languageKey, language);
      }
    });
  },

  /**
   * Gets the currently active language
   * @returns {string} - The active language code
   */
  getActive() {
    const activeButton = document.querySelector(`${ActiveStateConfig.selectors.languageButtons}.${ActiveStateConfig.classes.active}`);
    return activeButton ? activeButton.getAttribute('data-language') : null;
  },

  /**
   * Restores active state from cache
   */
  restoreFromCache() {
    const cachedLanguage = CacheManager.get(ActiveStateConfig.cache.languageKey);
    if (cachedLanguage) {
      this.setActive(cachedLanguage);
    } else {
      // Default to first language button if no cache
      const firstButton = document.querySelector(ActiveStateConfig.selectors.languageButtons);
      if (firstButton) {
        const defaultLang = firstButton.getAttribute('data-language');
        this.setActive(defaultLang);
      }
    }
  },

  /**
   * Initializes language active state management
   */
  init() {
    // Restore from cache or set default
    this.restoreFromCache();
    
    // Add click event listeners to language buttons
    const languageButtons = document.querySelectorAll(ActiveStateConfig.selectors.languageButtons);
    languageButtons.forEach(button => {
      button.addEventListener('click', (e) => {
        const language = e.currentTarget.getAttribute('data-language');
        if (language) {
          this.setActive(language);
        }
      });
    });
  }
};

/**
 * Theme Buttons Active State Manager
 */
const ThemeActiveManager = {
  /**
   * Sets active class for theme button
   * @param {string} theme - The theme value to activate
   */
  setActive(theme) {
    const themeButtons = document.querySelectorAll(ActiveStateConfig.selectors.themeButtons);
    
    themeButtons.forEach(button => {
      button.classList.remove(ActiveStateConfig.classes.active, ActiveStateConfig.classes.btnActive);
      
      if (button.getAttribute('data-theme-value') === theme) {
        button.classList.add(ActiveStateConfig.classes.active, ActiveStateConfig.classes.btnActive);
        CacheManager.set(ActiveStateConfig.cache.themeKey, theme);
      }
    });
  },

  /**
   * Gets the currently active theme
   * @returns {string} - The active theme value
   */
  getActive() {
    const activeButton = document.querySelector(`${ActiveStateConfig.selectors.themeButtons}.${ActiveStateConfig.classes.active}`);
    return activeButton ? activeButton.getAttribute('data-theme-value') : null;
  },

  /**
   * Restores active state from cache
   */
  restoreFromCache() {
    const cachedTheme = CacheManager.get(ActiveStateConfig.cache.themeKey);
    if (cachedTheme) {
      this.setActive(cachedTheme);
    } else {
      // Default to light theme if no cache
      this.setActive('light');
    }
  },

  /**
   * Initializes theme active state management
   */
  init() {
    // Restore from cache or set default
    this.restoreFromCache();
    
    // Add click event listeners to theme buttons
    const themeButtons = document.querySelectorAll(ActiveStateConfig.selectors.themeButtons);
    themeButtons.forEach(button => {
      button.addEventListener('click', (e) => {
        const theme = e.currentTarget.getAttribute('data-theme-value');
        if (theme) {
          this.setActive(theme);
        }
      });
    });
  }
};

/**
 * Main Active State Manager
 */
const ActiveStateManager = {
  /**
   * Initializes all active state managers
   */
  init() {
    try {
      NavActiveManager.init();
      LanguageActiveManager.init();
      ThemeActiveManager.init();
      
      console.log('Active State Manager initialized successfully');
    } catch (error) {
      console.error('Error initializing Active State Manager:', error);
    }
  },

  /**
   * Public API for external access
   */
  nav: NavActiveManager,
  language: LanguageActiveManager,
  theme: ThemeActiveManager,
  cache: CacheManager
};

/**
 * Initialize when DOM is ready
 */
function initializeActiveStateManager() {
  ActiveStateManager.init();
}

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    ActiveStateManager,
    NavActiveManager,
    LanguageActiveManager,
    ThemeActiveManager,
    CacheManager
  };
}
